//
//  NavigationManager.swift
//  wadar
//
//  Created by guan on 2025/6/26.
//

import SwiftUI
import Foundation
import WebKit

// MARK: - 导航管理器
class NavigationManager: NavigationManagerProtocol {
    @Published var navigationPath = NavigationPath()
    @Published var currentRoute: AppRoute = .splash

    // 保存通话前的状态
    private var preCallRoute: AppRoute?
    private var preCallStackDepth: Int = 0

    // WebView状态管理
    private weak var currentWebView: WKWebView?

    // 导航到指定路由
    func navigate(to route: AppRoute) {
        print("[NavigationManager] 导航到: \(route.title)")
        currentRoute = route
        navigationPath.append(route)
    }
    
    // 替换当前路由（不添加到导航栈）
    func replace(with route: AppRoute) {
        print("[NavigationManager] 替换当前页面为: \(route.title)")
        currentRoute = route
        // 清空导航栈并设置新的根页面
        navigationPath = NavigationPath()

        // 只有非根页面路由才添加到导航栈
        switch route {
        case .splash, .login, .webView:
            // 这些是根页面，不添加到导航栈
            break
        default:
            navigationPath.append(route)
        }
    }
    
    // 返回上一页
    func goBack() {
        print("[NavigationManager] 返回上一页")
        if !navigationPath.isEmpty {
            navigationPath.removeLast()
        }
    }
    
    // 返回到根页面
    func popToRoot() {
        print("[NavigationManager] 返回到根页面")
        navigationPath = NavigationPath()
        currentRoute = .splash
    }
    
    // 清除所有导航历史
    func reset() {
        print("[NavigationManager] 重置导航状态")
        navigationPath = NavigationPath()
        currentRoute = .splash
    }
    
    // 检查是否可以返回
    var canGoBack: Bool {
        return !navigationPath.isEmpty
    }
    
    // 获取导航栈深度
    var stackDepth: Int {
        return navigationPath.count
    }
}

// MARK: - 导航扩展方法
extension NavigationManager {
    
    // 处理登录成功后的导航
    func handleLoginSuccess(webViewURL: URL) {
        replace(with: .webView(webViewURL))
    }
    
    // 处理登出
    func handleLogout() {
        reset()
        replace(with: .login)
    }
    
    // 处理来电
    func handleIncomingCall() {
        savePreCallState()
        saveWebViewStateBeforeCall()
        navigate(to: .callIncoming)
    }

    // 处理外呼
    func handleOutgoingCall() {
        savePreCallState()
        saveWebViewStateBeforeCall()
        navigate(to: .callOutgoing)
    }
    
    // 处理通话连接
    func handleCallConnected() {
        replace(with: .callConnected)
    }
    
    // 处理通话结束 - 恢复到通话前的界面状态
    func handleCallEnded() {
        print("[NavigationManager] 处理通话结束，恢复到通话前的界面状态")
        restorePreCallState()
    }

    // 导航到ESP设备配网页面
    func navigateToESPProvision() {
        print("[NavigationManager] 导航到ESP设备配网页面")
        navigate(to: .espProvision)
    }

    // 恢复通话前的界面状态
    private func restorePreCallState() {
        print("[NavigationManager] 恢复通话前的界面状态")

        // 如果有保存的通话前状态，直接恢复
        if let savedRoute = preCallRoute {
            print("[NavigationManager] 恢复到保存的通话前状态: \(savedRoute.title)")

            // 清空当前导航栈
            navigationPath = NavigationPath()

            // 恢复到保存的路由
            currentRoute = savedRoute

            // 如果保存的路由不是根页面，需要添加到导航栈
            switch savedRoute {
            case .splash, .login, .webView:
                // 这些是根页面，不添加到导航栈
                // 如果是WebView，尝试恢复WebView状态
                if case .webView = savedRoute {
                    restoreWebViewStateAfterCall()
                }
                break
            default:
                navigationPath.append(savedRoute)
            }

        } else {
            // 如果没有保存的状态，尝试返回WebView首页
            print("[NavigationManager] 没有保存的通话前状态，返回WebView首页")
            returnToWebViewHome()
        }

        // 清除保存的通话前状态
        preCallRoute = nil
        preCallStackDepth = 0
    }

    // 返回WebView首页（保留作为备用方法）
    private func returnToWebViewHome() {
        // 获取当前登录状态和WebView URL
        if let webViewURL = getCurrentWebViewURL() {
            print("[NavigationManager] 返回WebView首页: \(webViewURL.absoluteString)")
            replace(with: .webView(webViewURL))
        } else {
            print("[NavigationManager] 无法获取WebView URL，返回登录页面")
            replace(with: .login)
        }
    }

    // 获取当前WebView URL
    private func getCurrentWebViewURL() -> URL? {
        // 首先检查当前路由是否是WebView
        if case .webView(let url) = currentRoute {
            return url
        }

        // 如果当前不是WebView，尝试从保存的状态获取
        if let savedRoute = preCallRoute, case .webView(let url) = savedRoute {
            return url
        }

        // 如果都没有，尝试从LoginViewModel获取
        // 这里需要访问全局的LoginViewModel实例
        return getWebViewURLFromLoginState()
    }

    // 从登录状态获取WebView URL
    private func getWebViewURLFromLoginState() -> URL? {
        // 使用UserManager获取保存的token
        guard let token = UserManager.shared.getToken() else {
            print("[NavigationManager] 无法获取保存的token")
            return nil
        }

        // 使用AppConfig获取base URL并构建WebView URL
        let webviewUrl = AppConfig.shared.webviewUrl
        let urlString = "\(webviewUrl)/pages/auth/app-auth/index?platform=iOS&appType=app&token=\(token)"

        print("[NavigationManager] 重新构建WebView URL: \(urlString)")
        return URL(string: urlString)
    }

    // 保存通话前的状态（保留此方法以备将来扩展使用）
    private func savePreCallState() {
        preCallRoute = currentRoute
        preCallStackDepth = stackDepth
        print("[NavigationManager] 保存通话前状态: \(currentRoute.title), 导航栈深度: \(stackDepth)")
    }

    // MARK: - WebView状态管理

    /// 设置当前WebView引用
    func setCurrentWebView(_ webView: WKWebView) {
        currentWebView = webView
    }

    /// 在VoIP通话开始前保存WebView状态
    private func saveWebViewStateBeforeCall() {
        guard let webView = currentWebView else {
            print("[NavigationManager] 没有WebView引用，无法保存状态")
            return
        }

        print("[NavigationManager] 保存WebView状态")
        WebViewManager.shared.saveWebViewStateBeforeCall(webView: webView)
    }

    /// 在VoIP通话结束后恢复WebView状态
    private func restoreWebViewStateAfterCall() {
        guard let webView = currentWebView else {
            print("[NavigationManager] 没有WebView引用，无法恢复状态")
            return
        }

        print("[NavigationManager] 恢复WebView状态")
        WebViewManager.shared.restoreWebViewStateAfterCall(webView: webView) { success in
            if success {
                print("[NavigationManager] WebView状态恢复成功")
            } else {
                print("[NavigationManager] WebView状态恢复失败")
            }
        }
    }
}
